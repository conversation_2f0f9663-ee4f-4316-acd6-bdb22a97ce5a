# Hybrid Scraper Improvements

## Overview

The main scraper has been enhanced with hybrid local + AI validation, increased context window utilization, and improved loop prevention. These changes address the issues with infinite loops and optimize the use of the 200,000 token context window.

## Key Improvements

### 1. Increased HTML Limits

**Before:**
- Job extraction: 300,000 characters
- Pagination: 75,000 characters

**After:**
- Job extraction: 800,000 characters (~200k tokens)
- Pagination: 200,000 characters

**Benefits:**
- Can send much more HTML content to AI without truncation
- Better extraction accuracy with full page context
- Reduced information loss

### 2. Hybrid Local + AI Validation

**How it works:**
1. On the first page, runs both local pattern matching and AI extraction
2. Compares the number of jobs found by each method
3. If results are similar (within 20% tolerance), continues with local method
4. If AI finds significantly more jobs, switches to AI-only mode for remaining pages

**Benefits:**
- Automatically chooses the best scraping method per site
- Faster execution when local patterns work well
- Falls back to AI when local patterns are insufficient
- Processes first page jobs during validation (no wasted work)

### 3. Enhanced Duplicate Detection

**Multiple mechanisms to prevent loops:**

#### URL-Based Loop Detection
- Tracks all visited URLs
- Stops immediately if the same URL is encountered again

#### Immediate Duplicate Stopping
- Stops scraping if a page adds 0 new jobs
- Stops scraping if a page adds fewer than 2 new jobs

#### Job Signature Tracking
- Enhanced duplicate detection across pages
- Prevents processing the same job multiple times

**Benefits:**
- Prevents infinite pagination loops
- Stops scraping as soon as duplicates are detected
- More efficient resource usage

### 4. AI-Only Scraping Mode

**When activated:**
- When hybrid validation determines local patterns are insufficient
- Maintains proper duplicate detection using job signatures
- Uses the same job processing pipeline

**Benefits:**
- Consistent behavior regardless of scraping method
- Proper duplicate handling in AI mode
- Seamless fallback mechanism

## Usage

### Running the Scraper

The scraper works exactly the same as before - no changes to the external interface:

```python
# Create scraper
scraper = ScraperFactory.create_scraper(site_name, config, db_manager, processor)

# Reset seen jobs (recommended for new scraping sessions)
if hasattr(scraper, 'reset_seen_jobs'):
    scraper.reset_seen_jobs()

# Run scraper
pages_scraped = await scraper.run_scraper(link)
```

### Testing the Implementation

Use the provided test scripts:

```bash
# Validate implementation
python validate_implementation.py

# Test with a real link (requires config.yaml)
python test_hybrid_scraper.py
```

## Configuration

No configuration changes are required. The scraper automatically:
- Detects the best scraping method per site
- Adjusts HTML limits based on content size
- Prevents loops using multiple detection mechanisms

## Logging

Enhanced logging provides visibility into the scraping process:

```
INFO - Running hybrid validation for Indeed
INFO - Validation results - Local: 15 jobs, AI: 16 jobs
INFO - Local and AI results similar (diff: 6.25%), using local method
INFO - Page 2: Only 1 new jobs added, likely hitting duplicates - stopping
INFO - Completed scraping for Indeed, scraped 2 pages
```

## Supported Sites

All existing scrapers benefit from these improvements:
- **Indeed**: Enhanced local patterns with AI fallback
- **StepStone**: Country-specific patterns with AI validation
- **OCC**: Jobcard extraction with AI enhancement
- **Glassdoor**: Local patterns with AI fallback
- **Unknown sites**: Automatic AI-only mode

## Performance Impact

**Positive impacts:**
- Faster scraping when local patterns work (no AI calls needed)
- Reduced API costs for sites with good local patterns
- Earlier termination prevents wasted processing
- Better extraction accuracy with larger context

**Considerations:**
- First page takes slightly longer due to validation
- AI-only mode uses more API calls but provides better accuracy
- Larger HTML content may increase processing time slightly

## Troubleshooting

### If scraping seems slow:
- Check logs to see if AI-only mode is being used
- Verify local patterns are working for your target sites
- Consider updating local patterns if they're outdated

### If loops still occur:
- Check logs for URL loop detection messages
- Verify duplicate detection is working
- Report the issue with specific site and URL

### If extraction quality is poor:
- Check if hybrid validation is choosing the right method
- Verify HTML limits are appropriate for your content
- Consider adjusting the 20% tolerance in validation

## Future Enhancements

Potential improvements:
- Machine learning to optimize the validation tolerance
- Site-specific HTML limit optimization
- Dynamic adjustment based on content complexity
- Performance metrics and optimization suggestions

## Files Modified

- `scrapers/ai_scraper.py`: Increased HTML limits
- `scrapers/base_scraper.py`: Added hybrid validation and enhanced duplicate detection
- `test_hybrid_scraper.py`: Test script for validation
- `validate_implementation.py`: Implementation validation script
