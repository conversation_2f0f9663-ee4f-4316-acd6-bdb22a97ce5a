from scrapfly import ScrapeConfig, ScrapflyClient
import logging
from bs4 import BeautifulSoup
import time

class BaseScraper:
    def __init__(self, config, db_manager, processor):
        self.config = config
        self.db_manager = db_manager
        self.processor = processor
        self.scrapfly = ScrapflyClient(key=config['scrapfly']['api_key'])

    async def scrape_page(self, url, site_name, city=None):
        """Scrape a single page and extract job listings"""
        try:
            # Use optimized config for Indeed (Germany works better)
            if 'indeed' in url.lower():
                scrape_config = ScrapeConfig(
                    url=url,
                    asp=True,  # Anti-scraping protection
                    country="DE"  # Germany works better for German Indeed
                )
            else:
                scrape_config = ScrapeConfig(
                    url=url,
                    asp=True,  # Anti-scraping protection
                    country="US"
                )

            result = await self.scrapfly.async_scrape(scrape_config)

            if result.status_code == 200:
                soup = BeautifulSoup(result.content, 'html.parser')

                # Extract job listings
                job_listings = self.extract_job_listings(soup, site_name)

                # Set city to exactly match the city from the scraping link
                if city:
                    for job in job_listings:
                        job['city'] = city  # Always use the city from the link

                # Process jobs with local pattern matching
                for job in job_listings:
                    # Process job data locally without API calls
                    processed_job = self.processor.process_job_locally(job)
                    self.db_manager.save_job(processed_job)

                # Find next page link if it exists
                next_page = self.extract_next_page(soup, url)
                return next_page
            else:
                logging.error(f"Failed to scrape {url}: Status code {result.status_code}")
                return None
        except Exception as e:
            logging.error(f"Error scraping {url}: {e}")
            return None

    def extract_job_listings(self, soup, site_name):
        """Extract job listings from the page - to be implemented by specific scrapers"""
        raise NotImplementedError("Subclasses must implement extract_job_listings")

    def extract_next_page(self, soup, current_url):
        """Extract next page URL - to be implemented by specific scrapers"""
        raise NotImplementedError("Subclasses must implement extract_next_page")

    async def run_scraper(self, link):
        """Run the scraper for a specific link with hybrid local+AI validation"""
        url = link['url']
        site_name = link['site_name']
        city = link.get('city', '')

        logging.info(f"Starting scraping for {site_name}: {url}")

        page_count = 0
        seen_job_signatures = set()  # Track job signatures to detect duplicates
        seen_urls = set()  # Track URLs to detect pagination loops
        max_pages = 10  # Maximum pages to scrape
        use_ai_only = False  # Flag to determine scraping method after first page validation

        while url and page_count < max_pages:
            page_count += 1
            logging.info(f"Scraping page {page_count}: {url}")

            # Check for URL-based loop detection
            if url in seen_urls:
                logging.warning(f"URL already seen, stopping to prevent loop: {url}")
                break
            seen_urls.add(url)

            # Get jobs count before scraping this page
            jobs_before = self.db_manager.get_jobs_count()

            # On first page, run hybrid validation if this scraper supports local extraction
            if page_count == 1 and hasattr(self, 'extract_job_listings') and not use_ai_only:
                use_ai_only = await self._validate_scraping_method(url, site_name, city)

            # Scrape the page
            if use_ai_only:
                next_page = await self._scrape_page_ai_only(url, site_name, city, seen_job_signatures)
            else:
                next_page = await self.scrape_page(url, site_name, city)

            # Get jobs count after scraping this page
            jobs_after = self.db_manager.get_jobs_count()
            jobs_added_this_page = jobs_after - jobs_before

            # Enhanced duplicate detection - stop immediately if page has mostly duplicates
            if page_count > 1 and jobs_added_this_page == 0:
                logging.info(f"Page {page_count}: No new jobs added, likely all duplicates - stopping")
                break
            elif page_count > 1 and jobs_added_this_page < 2:
                logging.info(f"Page {page_count}: Only {jobs_added_this_page} new jobs added, likely hitting duplicates - stopping")
                break

            if next_page == url:
                logging.warning(f"Next page is the same as current page: {url}")
                break

            url = next_page
            if url:
                # Add a delay between requests to avoid overloading the server
                time.sleep(self.config['scraping']['delay_seconds'])

        logging.info(f"Completed scraping for {site_name}, scraped {page_count} pages")
        return page_count

    async def _validate_scraping_method(self, url, site_name, city):
        """
        Validate scraping method by comparing local vs AI results on first page.
        Returns True if should use AI-only, False if local method is sufficient.
        """
        try:
            logging.info(f"Running hybrid validation for {site_name}")

            # Get the page content
            scrape_config = self._get_scrape_config(url)
            result = await self.scrapfly.async_scrape(scrape_config)

            if result.status_code != 200:
                logging.warning(f"Failed to fetch page for validation: {result.status_code}")
                return True  # Use AI as fallback

            soup = BeautifulSoup(result.content, 'html.parser')

            # Try local extraction
            local_jobs = []
            try:
                local_jobs = self.extract_job_listings(soup, site_name)
                local_count = len(local_jobs) if local_jobs else 0
            except Exception as e:
                logging.warning(f"Local extraction failed: {e}")
                local_count = 0

            # Try AI extraction
            ai_jobs = []
            try:
                from scrapers.ai_scraper import AIScraper
                ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
                ai_jobs = ai_scraper.extract_job_listings(soup, site_name)
                ai_count = len(ai_jobs) if ai_jobs else 0
            except Exception as e:
                logging.warning(f"AI extraction failed: {e}")
                ai_count = 0

            # Compare results
            logging.info(f"Validation results - Local: {local_count} jobs, AI: {ai_count} jobs")

            # If counts are similar (within 20% tolerance), use local method
            if local_count > 0 and ai_count > 0:
                difference_ratio = abs(local_count - ai_count) / max(local_count, ai_count)
                if difference_ratio <= 0.2:  # 20% tolerance
                    logging.info(f"Local and AI results similar (diff: {difference_ratio:.2%}), using local method")
                    # Process and save the local jobs since we already extracted them
                    await self._process_and_save_jobs(local_jobs, city)
                    return False  # Use local method
                else:
                    logging.info(f"Local and AI results differ significantly (diff: {difference_ratio:.2%}), using AI method")
                    # Process and save the AI jobs since they seem more accurate
                    await self._process_and_save_jobs(ai_jobs, city)
                    return True  # Use AI method
            elif ai_count > local_count:
                logging.info(f"AI found more jobs ({ai_count} vs {local_count}), using AI method")
                await self._process_and_save_jobs(ai_jobs, city)
                return True  # Use AI method
            elif local_count > 0:
                logging.info(f"Local method found jobs ({local_count}), AI found none, using local method")
                await self._process_and_save_jobs(local_jobs, city)
                return False  # Use local method
            else:
                logging.warning("Both methods found no jobs, using AI method as fallback")
                return True  # Use AI method

        except Exception as e:
            logging.error(f"Error during validation: {e}")
            return True  # Use AI as fallback

    async def _process_and_save_jobs(self, jobs, city):
        """Process and save jobs to database"""
        for job in jobs:
            # Set city to exactly match the city from the scraping link
            if city:
                job['city'] = city

            # Process job data locally without API calls
            processed_job = self.processor.process_job_locally(job)
            self.db_manager.save_job(processed_job)

    async def _scrape_page_ai_only(self, url, site_name, city, seen_job_signatures):
        """Scrape page using AI-only method with duplicate detection"""
        try:
            scrape_config = self._get_scrape_config(url)
            result = await self.scrapfly.async_scrape(scrape_config)

            if result.status_code == 200:
                soup = BeautifulSoup(result.content, 'html.parser')

                # Extract job listings using AI
                from scrapers.ai_scraper import AIScraper
                ai_scraper = AIScraper(self.config, self.db_manager, self.processor)
                job_listings = ai_scraper.extract_job_listings(soup, site_name, seen_job_signatures)

                # Process jobs with duplicate detection
                new_jobs_count = 0
                for job in job_listings:
                    # Create job signature for duplicate detection
                    job_signature = f"{job.get('title', '')}|{job.get('company', '')}|{job.get('location', '')}"

                    if job_signature not in seen_job_signatures:
                        seen_job_signatures.add(job_signature)

                        # Set city to exactly match the city from the scraping link
                        if city:
                            job['city'] = city

                        # Process job data locally without API calls
                        processed_job = self.processor.process_job_locally(job)
                        self.db_manager.save_job(processed_job)
                        new_jobs_count += 1

                logging.info(f"AI-only scraping: {new_jobs_count} new jobs added from {len(job_listings)} total")

                # Find next page link
                next_page = ai_scraper.extract_next_page(soup, url)
                return next_page
            else:
                logging.error(f"Failed to scrape {url}: Status code {result.status_code}")
                return None
        except Exception as e:
            logging.error(f"Error in AI-only scraping {url}: {e}")
            return None

    def _get_scrape_config(self, url):
        """Get appropriate scrape config for the URL"""
        if 'indeed' in url.lower():
            return ScrapeConfig(
                url=url,
                asp=True,  # Anti-scraping protection
                country="DE"  # Germany works better for German Indeed
            )
        else:
            return ScrapeConfig(
                url=url,
                asp=True,  # Anti-scraping protection
                country="US"
            )
