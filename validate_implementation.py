#!/usr/bin/env python3
"""
Validation script to check if the hybrid scraper implementation is working correctly
"""

import logging
from scrapers.ai_scraper import AIScraper
from scrapers.base_scraper import BaseScraper
from scrapers.scraper_factory import ScraperFactory

def validate_html_limits():
    """Validate that HTML limits have been increased"""
    print("1. Validating HTML limits...")

    # Check the source code for the updated limits
    try:
        with open('scrapers/ai_scraper.py', 'r') as f:
            content = f.read()

        # Check for job extraction limit
        if 'max_content_length = 800000' in content:
            print("   ✓ Job extraction limit correctly set to 800,000 characters")
        else:
            print("   ✗ Job extraction limit not found or incorrect")

        # Check for pagination limit
        if 'max_content_length = 200000  # Much larger limit for pagination' in content:
            print("   ✓ Pagination limit correctly set to 200,000 characters")
        else:
            print("   ✗ Pagination limit not found or incorrect")

    except Exception as e:
        print(f"   ✗ Error checking HTML limits: {e}")

def validate_hybrid_methods():
    """Validate that hybrid validation methods exist"""
    print("\n2. Validating hybrid validation methods...")

    try:
        # Check the source code for the new methods
        with open('scrapers/base_scraper.py', 'r') as f:
            content = f.read()

        methods_to_check = [
            '_validate_scraping_method',
            '_process_and_save_jobs',
            '_scrape_page_ai_only',
            '_get_scrape_config'
        ]

        for method_name in methods_to_check:
            if f'def {method_name}(' in content:
                print(f"   ✓ Method {method_name} exists")
            else:
                print(f"   ✗ Method {method_name} missing")

    except Exception as e:
        print(f"   ✗ Error validating hybrid methods: {e}")

def validate_duplicate_detection():
    """Validate enhanced duplicate detection"""
    print("\n3. Validating enhanced duplicate detection...")

    try:
        # Check the source code for enhanced duplicate detection
        with open('scrapers/base_scraper.py', 'r') as f:
            content = f.read()

        checks = [
            ('seen_urls = set()', 'URL-based loop detection'),
            ('use_ai_only = False', 'AI-only mode flag'),
            ('if url in seen_urls:', 'URL loop prevention'),
            ('jobs_added_this_page == 0:', 'Immediate duplicate stopping'),
            ('jobs_added_this_page < 2:', 'Early termination on few jobs')
        ]

        for check_text, description in checks:
            if check_text in content:
                print(f"   ✓ {description} implemented")
            else:
                print(f"   ✗ {description} missing")

    except Exception as e:
        print(f"   ✗ Error validating duplicate detection: {e}")

def validate_scraper_factory():
    """Validate that scraper factory works with new implementation"""
    print("\n4. Validating scraper factory...")

    try:
        # Check the source code for scraper factory
        with open('scrapers/scraper_factory.py', 'r') as f:
            content = f.read()

        test_sites = ['indeed', 'stepstone', 'occ', 'glassdoor']

        for site in test_sites:
            if f"'{site}' in site_name_lower" in content:
                print(f"   ✓ {site.title()} scraper supported")
            else:
                print(f"   ✗ {site.title()} scraper not found")

    except Exception as e:
        print(f"   ✗ Error validating scraper factory: {e}")

def validate_ai_scraper_compatibility():
    """Validate AI scraper compatibility with seen_jobs parameter"""
    print("\n5. Validating AI scraper compatibility...")

    try:
        # Check the source code for seen_jobs parameter
        with open('scrapers/ai_scraper.py', 'r') as f:
            content = f.read()

        if 'def extract_job_listings(self, soup, site_name, seen_jobs=None):' in content:
            print("   ✓ AI scraper extract_job_listings accepts seen_jobs parameter")
        else:
            print("   ✗ AI scraper extract_job_listings missing seen_jobs parameter")

    except Exception as e:
        print(f"   ✗ Error validating AI scraper compatibility: {e}")

def main():
    """Run all validation checks"""
    print("Hybrid Scraper Implementation Validation")
    print("=" * 50)
    
    # Configure logging to reduce noise during validation
    logging.getLogger().setLevel(logging.WARNING)
    
    try:
        validate_html_limits()
        validate_hybrid_methods()
        validate_duplicate_detection()
        validate_scraper_factory()
        validate_ai_scraper_compatibility()
        
        print("\n" + "=" * 50)
        print("Validation completed!")
        print("\nKey improvements implemented:")
        print("• HTML limits increased to utilize full 200k token context")
        print("• Hybrid local + AI validation on first page")
        print("• Enhanced duplicate detection with immediate stopping")
        print("• URL-based loop prevention")
        print("• AI-only mode for sites where local patterns fail")
        
    except Exception as e:
        print(f"\nValidation failed with error: {e}")
        logging.error("Validation error", exc_info=True)

if __name__ == "__main__":
    main()
