#!/usr/bin/env python3
"""
Test script to verify the hybrid scraper implementation
"""

import asyncio
import logging
import yaml
from scrapers.scraper_factory import ScraperFactory
from database.db_manager import DatabaseManager
from processors.job_processor import JobProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def test_hybrid_scraper():
    """Test the hybrid scraper with a sample link"""
    
    # Load configuration
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
    except FileNotFoundError:
        print("config.yaml not found. Please ensure it exists.")
        return
    
    # Initialize components
    db_manager = DatabaseManager(config)
    processor = JobProcessor(config)
    
    # Test link (you can modify this)
    test_link = {
        'url': 'https://de.indeed.com/jobs?q=software+engineer&l=Berlin',
        'site_name': 'Indeed',
        'city': 'Berlin'
    }
    
    print(f"Testing hybrid scraper with: {test_link['site_name']}")
    print(f"URL: {test_link['url']}")
    print(f"City: {test_link['city']}")
    print("-" * 50)
    
    # Create scraper
    scraper = ScraperFactory.create_scraper(
        test_link['site_name'], 
        config, 
        db_manager, 
        processor
    )
    
    # Reset seen jobs
    if hasattr(scraper, 'reset_seen_jobs'):
        scraper.reset_seen_jobs()
    
    # Get initial job count
    cursor = db_manager.connection.cursor()
    cursor.execute("SELECT COUNT(*) FROM jobs")
    initial_count = cursor.fetchone()[0]
    print(f"Initial job count in database: {initial_count}")
    
    try:
        # Run the scraper
        pages_scraped = await scraper.run_scraper(test_link)
        
        # Get final job count
        cursor.execute("SELECT COUNT(*) FROM jobs")
        final_count = cursor.fetchone()[0]
        jobs_added = final_count - initial_count
        
        print("-" * 50)
        print(f"Scraping completed!")
        print(f"Pages scraped: {pages_scraped}")
        print(f"Jobs added: {jobs_added}")
        print(f"Final job count: {final_count}")
        
        # Show some sample jobs
        if jobs_added > 0:
            cursor.execute("""
                SELECT title, company, location, source_site 
                FROM jobs 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            recent_jobs = cursor.fetchall()
            
            print("\nRecent jobs added:")
            for job in recent_jobs:
                print(f"  - {job[0]} at {job[1]} ({job[2]}) [{job[3]}]")
        
    except Exception as e:
        print(f"Error during scraping: {e}")
        logging.error(f"Scraping error: {e}", exc_info=True)
    
    finally:
        # Close database connection
        db_manager.close()

if __name__ == "__main__":
    print("Hybrid Scraper Test")
    print("=" * 50)
    print("This test will:")
    print("1. Load configuration")
    print("2. Test hybrid validation (local + AI)")
    print("3. Test enhanced duplicate detection")
    print("4. Test loop prevention")
    print("5. Show results")
    print("=" * 50)
    
    asyncio.run(test_hybrid_scraper())
